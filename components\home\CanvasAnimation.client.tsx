import { useEffect, useRef, useState } from 'react';

declare global {
  interface Window {
    createjs: any;
  }
}

const loadCreateJS = (callback: () => void) => {
  if (window.createjs) {
    callback();
    return;
  }
  const script = document.createElement('script');
  script.src = 'https://code.createjs.com/1.0.0/createjs.min.js';
  script.onload = () => {
    callback();
  };
  document.head.appendChild(script);
};

const CanvasAnimation = () => {
  const canvasContainerRef = useRef<HTMLDivElement>(null);
  const [themeKey, setThemeKey] = useState(0);

  useEffect(() => {
    let stage: any;
    let handleResize: (curves?: any) => void;
    let observer: MutationObserver;

    const cleanup = () => {
      if (observer) observer.disconnect();
      if (window.createjs && stage) {
        window.createjs.Ticker.removeEventListener("tick", stage);
        stage.removeAllChildren();
        stage.update();
      }
      if (canvasContainerRef.current) {
        canvasContainerRef.current.innerHTML = '';
      }
      window.removeEventListener("resize", handleResize);
      stage = null;
    };

    const setupAnimation = () => {
      // Ensure cleanup runs before setting up a new animation
      if (stage) {
        if (window.createjs) {
          window.createjs.Ticker.removeEventListener("tick", stage);
          stage.removeAllChildren();
          stage.update();
        }
        stage = null;
      }
      if (canvasContainerRef.current) {
        canvasContainerRef.current.innerHTML = '';
      }

      loadCreateJS(() => {
        if (!window.createjs || !canvasContainerRef.current) return;

        const radian = 100;
        let curves: any;

        const init = () => {
          createCanvas();

          stage = new window.createjs.Stage('myCanvas');
          curves = new Curves();
          curves['x'] = window.innerWidth / 2;
          curves['y'] = window.innerHeight / 2;
          stage.addChild(curves);
          window.createjs.Ticker.addEventListener("tick", stage);
          window.createjs.Ticker.timingMode = window.createjs.Ticker.RAF;
          window.addEventListener("resize", () => handleResize(curves), false);
          handleResize(curves);
          stage.update();
        };

        const createCanvas = () => {
          if (!canvasContainerRef.current) return;
          const div = document.createElement('div');
          const canvas = document.createElement("canvas");
          canvas.setAttribute("id", "myCanvas");
          div.appendChild(canvas);
          canvasContainerRef.current.appendChild(div);

          const myCanvas = document.getElementById('myCanvas') as HTMLCanvasElement;
          myCanvas.style.width = "100%";
          myCanvas.style.height = "100%";
          myCanvas.style.display = "block";
          
          // Remove manual background color - we'll use Tailwind classes on the container
          myCanvas.style.backgroundColor = 'transparent';

          myCanvas.style.position = "absolute";
          myCanvas.style.top = "0";
          myCanvas.style.left = "0";
        };

        // Function to get current theme colors
        const getCurrentColors = () => {
          const isDarkMode = document.documentElement.classList.contains('dark');
          const lineColor = isDarkMode ? "#1a202c" : "#ffffff";
          const accentColor = isDarkMode ? "#171923" : "#f9f9f9";
          console.log('CanvasAnimation - isDarkMode:', isDarkMode, 'lineColor:', lineColor, 'accentColor:', accentColor);
          return { lineColor, accentColor };
        };

        const { lineColor, accentColor } = getCurrentColors();

        class Line extends window.createjs.Shape {
          constructor(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, i: number, color: string) {
              super();
              this['graphics'].beginStroke(color).setStrokeStyle(0.2).moveTo(x1,y1);
              var cmd = this['graphics'].lineTo(x1, y1).command;
              window.createjs.Tween.get(cmd, {loop: true})
              .to({x:x2, y:y2},4000, window.createjs.Ease.cubicInOut)
              .to({x:x3, y:y3}, 4000, window.createjs.Ease.cubicInOut)
              .to({x:x2, y:y2}, 4000, window.createjs.Ease.cubicInOut)
              .to({x:x3, y:y2}, 4000, window.createjs.Ease.cubicInOut)
              .to({x:x2, y:y2}, 4000, window.createjs.Ease.cubicInOut)
              .to({x:x1, y:y1}, 4000, window.createjs.Ease.cubicInOut)
          }
          update() {}
        }

        class Curve extends window.createjs.Container {
          constructor(x: number, y: number, rad: number, reflect: number, count: number, color: string) {
              super();
              this['x'] = x*radian-radian*2;
              this['y'] = y*radian-radian*2;
              this['regX'] = radian*2;
              this['regY'] = radian*2;
              this['rotation'] = rad;
              this['scaleX'] *= reflect;

              for(var i = 0; i < 180; i++){
                  var x1 = radian*Math.sin(i*180/90*Math.PI/90)+radian;
                  var y1 = radian*Math.cos(i*180/90*Math.PI/90)+radian;
                  var x2 = radian/2*Math.sin((i+count)*180/45*Math.PI/90)+radian*3;
                  var y2 = radian/2*Math.cos((i+count)*180/90*Math.PI/90)+radian*3;
                  var x3 = radian/2*Math.sin(((i+45)+count)*180/45*Math.PI/90)+radian*3;
                  var y3 = radian/2*Math.cos(((i+45)+count)*180/90*Math.PI/90)+radian*3;
                  var line_inst  = new Line(x1, y1, x2, y2, x3, y3, i, color);
                  this['addChild'](line_inst);
              }
          }
          update() {}
        }

        class Curves extends window.createjs.Container {
          constructor() {
              super();
              this['scaleX'] = this['scaleY'] = 1;

              var curve1_1 = new Curve(0, 0, 0, -1, 20, lineColor);
              this['addChild'](curve1_1);
              var curve1_2 = new Curve(0, 2, 180, 1, 12, accentColor);
              this['addChild'](curve1_2);
              var curve1_3 = new Curve(2, -2, 180, -1, 52, lineColor);
              this['addChild'](curve1_3);
              var curve1_4 = new Curve(4, -2, 180, 1, 82, accentColor);
              this['addChild'](curve1_4);
              var curve1_5 = new Curve(6, 0, 0, 1, 42, lineColor);
              this['addChild'](curve1_5);

              var curve2_1 = new Curve(0, 4, -90, -1, 62, lineColor);
              this['addChild'](curve2_1);
              var curve2_2 = new Curve(2, 4, 90, 1, 22, accentColor);
              this['addChild'](curve2_2);
              var curve2_3 = new Curve(-2, 2, 90, -1, 92, lineColor);
              this['addChild'](curve2_3);
              var curve2_4 = new Curve(-2, 0, 90, 1, 42, lineColor);
              this['addChild'](curve2_4);
              var curve2_5 = new Curve(0, -2, 270, 1, 12, lineColor);
              this['addChild'](curve2_5);

              var curve3_1 = new Curve(4, 4, -180, -1, 62, lineColor);
              this['addChild'](curve3_1);
              var curve3_2 = new Curve(4, 2, 0, 1, 22, lineColor);
              this['addChild'](curve3_2);
              var curve3_3 = new Curve(2, 6, 0, -1, 29, lineColor);
              this['addChild'](curve3_3);
              var curve3_4 = new Curve(0, 6, 0, 1, 21, lineColor);
              this['addChild'](curve3_4);
              var curve3_5 = new Curve(-2, 4, 180, 1, 100, lineColor);
              this['addChild'](curve3_5);

              var curve4_1 = new Curve(4, 0, -270, -1, 2, accentColor);
              this['addChild'](curve4_1);
              var curve4_2 = new Curve(2, 0, -90, 1, 10, lineColor);
              this['addChild'](curve4_2);
              var curve4_3 = new Curve(6, 2, -90, -1, 40, lineColor);
              this['addChild'](curve4_3);
              var curve4_4 = new Curve(6, 4, -90, 1, 140, lineColor);
              this['addChild'](curve4_4);
              var curve4_5 = new Curve(4, 6, -270, 1, 60, lineColor);
              this['addChild'](curve4_5);
              this['on']('tick', this.update, this);
          }
          update() {
              if (window.innerWidth > 768) {
                  this['scaleX'] = this['scaleY'] = window.innerWidth/1200;
              } else {
                  this['scaleX'] = this['scaleY'] = window.innerWidth/600;
              }
          }
        }

        handleResize = (curves?: any) => {
          if (!stage || !stage.canvas) return;
          stage.canvas.width = window.innerWidth;
          stage.canvas.height = window.innerHeight;
          if (curves) {
              curves['x'] = window.innerWidth / 2;
              curves['y'] = window.innerHeight / 2;
          }
          stage.update();
        };

        init();
      });
    }

    // Initial setup
    setupAnimation();

    // Observe theme changes and re-initialize animation
    const mutationCallback = (mutationsList: MutationRecord[]) => {
      for(const mutation of mutationsList) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          cleanup();
          // Force complete re-render by updating theme key
          setThemeKey(prev => prev + 1);
        }
      }
    };

    observer = new MutationObserver(mutationCallback);
    observer.observe(document.documentElement, { attributes: true });

    return cleanup;
  }, [themeKey]);

  return <div ref={canvasContainerRef} className="fixed top-0 left-0 w-full h-full -z-10 bg-[#f2f2f2] dark:bg-[#0f1419]" />;
};

export default CanvasAnimation;